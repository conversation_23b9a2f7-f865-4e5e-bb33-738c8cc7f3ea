import { clipboard, nativeImage } from 'electron'
import fs from 'node:fs'
import path from 'node:path'

// 文件类型定义
const FILE_TYPES = {
  IMAGE: 'image',
  TEXT: 'text',
  BINARY: 'binary',
}

// 图片文件扩展名
const IMAGE_EXTENSIONS = new Set([
  '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico',
  '.tiff', '.tif', '.avif', '.heic', '.heif',
])

// 文本文件扩展名
const TEXT_EXTENSIONS = new Set([
  '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx', '.html', '.htm',
  '.css', '.scss', '.sass', '.less', '.xml', '.yaml', '.yml', '.csv',
  '.log', '.ini', '.conf', '.config', '.sh', '.bat', '.cmd', '.ps1',
  '.py', '.java', '.c', '.cpp', '.h', '.hpp', '.cs', '.php', '.rb',
  '.go', '.rs', '.swift', '.kt', '.scala', '.sql', '.r', '.m', '.pl',
  '.lua', '.vim', '.dockerfile', '.gitignore', '.env', '.editorconfig',
])

// 文本文件的最大大小限制（10MB）
const MAX_TEXT_FILE_SIZE = 10 * 1024 * 1024

/**
 * 检测文件类型
 * @param {string} filePath - 文件路径
 * @returns {string} - 文件类型
 */
function detectFileType(filePath) {
  const ext = path.extname(filePath).toLowerCase()

  if (IMAGE_EXTENSIONS.has(ext)) {
    return FILE_TYPES.IMAGE
  }

  if (TEXT_EXTENSIONS.has(ext)) {
    return FILE_TYPES.TEXT
  }

  return FILE_TYPES.BINARY
}

/**
 * 复制图片文件到剪切板
 * @param {string} filePath - 图片文件路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
async function copyImageToClipboard(filePath) {
  try {
    const imageBuffer = fs.readFileSync(filePath)
    const image = nativeImage.createFromBuffer(imageBuffer)

    if (image.isEmpty()) {
      throw new Error('Failed to create image from file buffer')
    }

    clipboard.writeImage(image)
    console.log(`Successfully copied image to clipboard: ${filePath}`)
    return true
  }
  catch (error) {
    console.error('Failed to copy image to clipboard:', error.message)
    return false
  }
}

/**
 * 复制文本文件内容到剪切板
 * @param {string} filePath - 文本文件路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
async function copyTextToClipboard(filePath) {
  try {
    const stats = fs.statSync(filePath)

    // 检查文件大小
    if (stats.size > MAX_TEXT_FILE_SIZE) {
      console.warn(`Text file too large (${stats.size} bytes), copying file path instead`)
      clipboard.writeText(filePath)
      console.log(`Successfully copied file path to clipboard: ${filePath}`)
      return true
    }

    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8')
    clipboard.writeText(content)
    console.log(`Successfully copied text content to clipboard: ${filePath}`)
    return true
  }
  catch (error) {
    console.error('Failed to copy text to clipboard:', error.message)
    // 如果读取文本失败，尝试复制文件路径
    try {
      clipboard.writeText(filePath)
      console.log(`Fallback: copied file path to clipboard: ${filePath}`)
      return true
    }
    catch (fallbackError) {
      console.error('Failed to copy file path to clipboard:', fallbackError.message)
      return false
    }
  }
}

/**
 * 复制二进制文件到剪切板（复制文件路径）
 * @param {string} filePath - 二进制文件路径
 * @returns {Promise<boolean>} - 操作是否成功
 */
async function copyBinaryToClipboard(filePath) {
  try {
    // 对于二进制文件，复制文件路径到剪切板
    clipboard.writeText(filePath)
    console.log(`Successfully copied file path to clipboard: ${filePath}`)
    return true
  }
  catch (error) {
    console.error('Failed to copy file path to clipboard:', error.message)
    return false
  }
}

/**
 * 将文件复制到系统剪切板（通用版本）
 * 支持 macOS、Windows、Linux 平台
 * 支持图片、文本、二进制等各种类型的文件
 *
 * @param {string} filePath - 要复制的文件路径
 * @param {Object} options - 选项配置
 * @param {boolean} options.forceFilePath - 强制复制文件路径而不是文件内容
 * @returns {Promise<boolean>} - 操作是否成功
 */
export async function copyFileToClipboard(filePath, options = {}) {
  const { forceFilePath = false } = options

  try {
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      throw new Error(`File does not exist: ${filePath}`)
    }

    // 检查是否是文件而不是目录
    const stats = fs.statSync(filePath)
    if (!stats.isFile()) {
      throw new Error(`Path is not a file: ${filePath}`)
    }

    // 如果强制复制文件路径，直接复制路径
    if (forceFilePath) {
      clipboard.writeText(filePath)
      console.log(`Successfully copied file path to clipboard: ${filePath}`)
      return true
    }

    // 检测文件类型并选择相应的复制策略
    const fileType = detectFileType(filePath)

    switch (fileType) {
      case FILE_TYPES.IMAGE:
        return await copyImageToClipboard(filePath)

      case FILE_TYPES.TEXT:
        return await copyTextToClipboard(filePath)

      case FILE_TYPES.BINARY:
      default:
        return await copyBinaryToClipboard(filePath)
    }
  }
  catch (error) {
    console.error('Failed to copy file to clipboard:', error.message)
    return false
  }
}
