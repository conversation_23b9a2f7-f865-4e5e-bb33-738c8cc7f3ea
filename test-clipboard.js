// 测试优化后的 copyFileToClipboard 函数
import { copyFileToClipboard } from './electron/helpers/clipboard.js'
import fs from 'node:fs'
import path from 'node:path'

// 创建测试文件
const testDir = './test-files'
if (!fs.existsSync(testDir)) {
  fs.mkdirSync(testDir)
}

// 创建测试文本文件
const textFile = path.join(testDir, 'test.txt')
fs.writeFileSync(textFile, 'Hello, this is a test text file!\nLine 2\nLine 3')

// 创建测试 JSON 文件
const jsonFile = path.join(testDir, 'test.json')
fs.writeFileSync(jsonFile, JSON.stringify({ message: 'Hello World', version: '1.0.0' }, null, 2))

// 创建测试 JavaScript 文件
const jsFile = path.join(testDir, 'test.js')
fs.writeFileSync(jsFile, `console.log('Hello from test.js');\nconst message = 'This is a test';\nexport { message };`)

async function runTests() {
  console.log('开始测试 copyFileToClipboard 函数...\n')

  // 测试 1: 复制文本文件内容
  console.log('测试 1: 复制文本文件内容')
  const result1 = await copyFileToClipboard(textFile)
  console.log(`结果: ${result1 ? '成功' : '失败'}\n`)

  // 测试 2: 复制 JSON 文件内容
  console.log('测试 2: 复制 JSON 文件内容')
  const result2 = await copyFileToClipboard(jsonFile)
  console.log(`结果: ${result2 ? '成功' : '失败'}\n`)

  // 测试 3: 复制 JavaScript 文件内容
  console.log('测试 3: 复制 JavaScript 文件内容')
  const result3 = await copyFileToClipboard(jsFile)
  console.log(`结果: ${result3 ? '成功' : '失败'}\n`)

  // 测试 4: 强制复制文件路径
  console.log('测试 4: 强制复制文件路径')
  const result4 = await copyFileToClipboard(textFile, { forceFilePath: true })
  console.log(`结果: ${result4 ? '成功' : '失败'}\n`)

  // 测试 5: 测试不存在的文件
  console.log('测试 5: 测试不存在的文件')
  const result5 = await copyFileToClipboard('./non-existent-file.txt')
  console.log(`结果: ${result5 ? '成功' : '失败'} (应该失败)\n`)

  // 测试 6: 测试目录而不是文件
  console.log('测试 6: 测试目录而不是文件')
  const result6 = await copyFileToClipboard(testDir)
  console.log(`结果: ${result6 ? '成功' : '失败'} (应该失败)\n`)

  // 清理测试文件
  fs.rmSync(testDir, { recursive: true, force: true })
  console.log('测试完成，已清理测试文件')
}

// 运行测试
runTests().catch(console.error)
